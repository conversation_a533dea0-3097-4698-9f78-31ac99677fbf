/**
 * GLM-Highlight 选项页面脚本
 * 用于管理域名白名单/黑名单的操作
 */

// 域名存储常量
const DOMAIN_RULES_KEY = "domain_rules";
// 移除了划词工具栏设置存储键

// 定义数据结构
const defaultRules = {
  mode: "blacklist", // 默认为黑名单模式，更符合用户期望
  domains: [], // 域名数组
};

// DOM 元素引用
const elements = {
  modeRadios: document.querySelectorAll('input[name="mode"]'),
  // 移除了划词工具栏单选按钮引用
  saveBtn: document.getElementById("saveBtn"),
  exportBtn: document.getElementById("exportBtn"),
  importBtn: document.getElementById("importBtn"),
  importFile: document.getElementById("importFile"),
  addNewDomain: document.getElementById("addNewDomain"),
  domainInputContainer: document.getElementById("domainInputContainer"),
  newDomainInput: document.getElementById("newDomainInput"),
  saveDomainBtn: document.getElementById("saveDomainBtn"),
  cancelDomainBtn: document.getElementById("cancelDomainBtn"),
  domainList: document.getElementById("domainList"),
  emptyState: document.getElementById("emptyState"),
  saveStatus: document.getElementById("saveStatus"),
};

// 保存状态对象
let domainRules = { ...defaultRules };
let isFormDirty = false;
// 移除了划词工具栏设置状态

// 初始化
document.addEventListener("DOMContentLoaded", async () => {
  // 加载保存的域名规则
  await loadDomainRules();

  // 设置界面初始状态
  updateUI();

  // 事件绑定
  setupEventListeners();
});

/**
 * 加载已保存的域名规则
 */
async function loadDomainRules() {
  try {
    const result = await chrome.storage.sync.get([DOMAIN_RULES_KEY]);
    if (result[DOMAIN_RULES_KEY]) {
      domainRules = result[DOMAIN_RULES_KEY];
    } else {
      domainRules = { ...defaultRules };
      // 首次加载时保存默认规则
      await chrome.storage.sync.set({ [DOMAIN_RULES_KEY]: domainRules });
    }

    // 移除了划词工具栏设置加载
  } catch (error) {
    console.error("加载配置失败:", error);
    showSaveStatus("加载配置失败", "error");
  }
}

/**
 * 保存域名规则
 */
async function saveDomainRules() {
  try {
    // 保存域名规则
    await chrome.storage.sync.set({ [DOMAIN_RULES_KEY]: domainRules });

    // 移除了划词工具栏设置保存

    // 通知后台脚本规则已更新
    await chrome.runtime.sendMessage({
      opt: "event",
      event: "domainRulesUpdated",
      data: domainRules,
    });

    // 移除了划词工具栏设置更新通知

    isFormDirty = false;

    // 提供更清晰的说明，确保用户理解需要刷新标签页
    showSaveStatus(
      "保存成功！重要提示：设置只会在页面刷新后生效，不会立即应用到当前打开的页面。请刷新需要应用规则的标签页查看效果。",
      "success"
    );
  } catch (error) {
    console.error("保存配置失败:", error);
    showSaveStatus("保存失败", "error");
  }
}

/**
 * 更新UI状态
 */
function updateUI() {
  // 设置模式单选框
  Array.from(elements.modeRadios).find(
    (radio) => radio.value === domainRules.mode
  ).checked = true;

  // 移除了划词工具栏单选框设置

  // 清空并重绘域名列表
  renderDomainList();
}

/**
 * 渲染域名列表
 */
function renderDomainList() {
  elements.domainList.innerHTML = "";

  // 判断是否显示空状态
  if (domainRules.domains.length === 0) {
    elements.emptyState.style.display = "block";
    return;
  }

  elements.emptyState.style.display = "none";

  // 渲染域名列表
  domainRules.domains.forEach((domain, index) => {
    const row = document.createElement("tr");
    row.innerHTML = `
      <td class="domain-cell">${domain}</td>
      <td class="action-cell">
        <button class="btn btn-small btn-danger" onclick="removeDomain(${index})">
          删除
        </button>
      </td>
    `;
    elements.domainList.appendChild(row);
  });
}

/**
 * 添加域名
 */
function addDomain() {
  const domain = elements.newDomainInput.value.trim();

  if (!domain) {
    showSaveStatus("请输入域名", "error");
    return;
  }

  // 验证域名格式
  if (!isValidDomain(domain)) {
    showSaveStatus("域名格式不正确", "error");
    return;
  }

  // 检查是否已存在
  if (domainRules.domains.includes(domain)) {
    showSaveStatus("域名已存在", "error");
    return;
  }

  // 添加到列表
  domainRules.domains.push(domain);
  isFormDirty = true;

  // 清空输入框
  elements.newDomainInput.value = "";

  // 隐藏输入容器
  elements.domainInputContainer.style.display = "none";

  // 重新渲染列表
  renderDomainList();

  showSaveStatus("域名已添加，请点击保存按钮", "success");
}

/**
 * 删除域名
 */
function removeDomain(index) {
  if (confirm("确定要删除这个域名吗？")) {
    domainRules.domains.splice(index, 1);
    isFormDirty = true;
    renderDomainList();
    showSaveStatus("域名已删除，请点击保存按钮", "success");
  }
}

/**
 * 验证域名格式
 */
function isValidDomain(domain) {
  // 支持通配符域名
  if (domain.startsWith("*.")) {
    domain = domain.slice(2);
  }

  // 基本域名格式验证
  const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
  return domainRegex.test(domain);
}

/**
 * 显示保存状态
 */
function showSaveStatus(message, type = "info") {
  elements.saveStatus.textContent = message;
  elements.saveStatus.className = `save-status ${type}`;

  // 3秒后清除状态
  setTimeout(() => {
    elements.saveStatus.textContent = "";
    elements.saveStatus.className = "save-status";
  }, 3000);
}

/**
 * 导出规则
 */
function exportRules() {
  const data = {
    domainRules,
    exportTime: new Date().toISOString(),
    version: "1.0",
  };

  const blob = new Blob([JSON.stringify(data, null, 2)], {
    type: "application/json",
  });
  const url = URL.createObjectURL(blob);

  const a = document.createElement("a");
  a.href = url;
  a.download = `glm-highlight-rules-${new Date().toISOString().split("T")[0]}.json`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);

  showSaveStatus("规则已导出", "success");
}

/**
 * 导入规则
 */
function importRules(event) {
  const file = event.target.files[0];
  if (!file) return;

  const reader = new FileReader();
  reader.onload = function (e) {
    try {
      const data = JSON.parse(e.target.result);

      if (data.domainRules) {
        domainRules = data.domainRules;
        isFormDirty = true;
        updateUI();
        showSaveStatus("规则已导入，请点击保存按钮", "success");
      } else {
        showSaveStatus("导入文件格式不正确", "error");
      }
    } catch (error) {
      showSaveStatus("导入失败：文件格式错误", "error");
    }
  };

  reader.readAsText(file);
}

/**
 * 设置事件监听
 */
function setupEventListeners() {
  // 模式变更
  elements.modeRadios.forEach((radio) => {
    radio.addEventListener("change", () => {
      // 只更新内存中的数据，不触发保存
      domainRules.mode = radio.value;
      isFormDirty = true;

      // 清晰地提示用户需要点击保存按钮才会生效
      showSaveStatus("模式已更改，请点击「保存配置」按钮使更改生效", "success");

      // 强调保存按钮
      elements.saveBtn.classList.add("highlight-btn");
      setTimeout(() => {
        elements.saveBtn.classList.remove("highlight-btn");
      }, 1500);
    });
  });

  // 移除了划词工具栏单选框事件监听

  // 保存按钮
  elements.saveBtn.addEventListener("click", () => {
    saveDomainRules();
  });

  // 导出按钮
  elements.exportBtn.addEventListener("click", exportRules);

  // 导入按钮
  elements.importBtn.addEventListener("click", () => {
    elements.importFile.click();
  });

  // 导入文件
  elements.importFile.addEventListener("change", importRules);

  // 添加域名按钮
  elements.addNewDomain.addEventListener("click", () => {
    elements.domainInputContainer.style.display = "block";
    elements.newDomainInput.focus();
  });

  // 保存域名按钮
  elements.saveDomainBtn.addEventListener("click", addDomain);

  // 取消添加域名按钮
  elements.cancelDomainBtn.addEventListener("click", () => {
    elements.domainInputContainer.style.display = "none";
    elements.newDomainInput.value = "";
  });

  // 域名输入框回车事件
  elements.newDomainInput.addEventListener("keypress", (e) => {
    if (e.key === "Enter") {
      addDomain();
    }
  });

  // 页面离开前提醒
  window.addEventListener("beforeunload", (e) => {
    if (isFormDirty) {
      e.preventDefault();
      e.returnValue = "您有未保存的更改，确定要离开吗？";
    }
  });
}

// 全局函数，供HTML调用
window.removeDomain = removeDomain;
