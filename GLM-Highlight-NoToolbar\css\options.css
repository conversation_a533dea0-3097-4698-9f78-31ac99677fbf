/* 基础样式 */
:root {
  --primary-color: #4d7cff;
  --primary-light: #6c93ff;
  --secondary-color: #409eff;
  --text-color: #303133;
  --bg-color: #f9fafc;
  --card-bg: #ffffff;
  --border-color: #ebeef5;
  --success-color: #67c23a;
  --error-color: #f56c6c;
  --warning-color: #e6a23c;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background: var(--bg-color);
  padding: 24px;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* 标题区域 */
.header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: white;
  padding: 24px 32px;
  border-bottom: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
}

.header:before {
  content: "";
  position: absolute;
  top: -10px;
  right: -10px;
  width: 200px;
  height: 200px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.header h1 {
  font-size: 24px;
  margin-bottom: 8px;
  font-weight: 600;
  position: relative;
  z-index: 1;
}

.subtitle {
  font-size: 15px;
  opacity: 0.95;
  position: relative;
  z-index: 1;
}

/* 主内容区域 - 左右布局 */
.main-content {
  display: flex;
  flex-wrap: wrap;
  min-height: 500px;
  padding: 12px;
  gap: 20px;
}

.left-panel, .right-panel {
  padding: 16px;
}

.left-panel {
  flex: 0 0 300px;
  border-right: none;
}

.right-panel {
  flex: 1;
  min-width: 300px;
}

/* 卡片样式 */
.card {
  background: var(--card-bg);
  border-radius: 8px;
  padding: 24px;
  height: 100%;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.card h2 {
  font-size: 18px;
  margin-bottom: 20px;
  color: var(--text-color);
  font-weight: 600;
  border-bottom: none;
  padding-bottom: 0;
  position: relative;
}

.card h2:after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -8px;
  width: 40px;
  height: 3px;
  background: var(--primary-color);
  border-radius: 2px;
}

/* 按钮样式 - 与插件保持一致 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 9px 18px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
  line-height: 1.2;
  height: 36px;
  font-weight: 500;
}

.btn:hover {
  background: var(--primary-light);
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(77, 124, 255, 0.2);
}

.btn-primary {
  background: var(--primary-color);
}

.btn-secondary {
  background: rgba(144, 147, 153, 0.8);
}

.btn-secondary:hover {
  background: rgba(144, 147, 153, 0.9);
}

.btn-small {
  padding: 5px 12px;
  font-size: 12px;
  height: 28px;
}

.icon {
  margin-right: 5px;
}

/* 单选框样式 - 使用EL样式 */
.mode-selector {
  margin-bottom: 24px;
}

.radio-container {
  display: block;
  position: relative;
  padding-left: 32px;
  margin-bottom: 16px;
  cursor: pointer;
  font-size: 15px;
  line-height: 20px;
}

.radio-container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.radio-container .radio-label:before {
  content: '';
  position: absolute;
  left: 0;
  top: 1px;
  width: 18px;
  height: 18px;
  border: 1px solid #dcdfe6;
  border-radius: 50%;
  transition: all .2s;
}

.radio-container input:checked ~ .radio-label:after {
  content: '';
  position: absolute;
  left: 4px;
  top: 5px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: var(--primary-color);
  transform: translate(0, 0);
}

.radio-container input:checked ~ .radio-label:before {
  border-color: var(--primary-color);
}

/* 按钮组 */
.buttons-group {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

/* 保存按钮区域 */
.save-section {
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.save-status {
  font-size: 12px;
  height: 20px;
}

.save-success {
  color: var(--success-color);
}

.save-error {
  color: var(--error-color);
}

/* 高亮保存按钮样式 */
.highlight-btn {
  animation: pulse 1.5s ease-in-out;
  transform: scale(1.05);
  box-shadow: 0 0 8px rgba(77, 124, 255, 0.6);
  transition: all 0.3s ease;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(77, 124, 255, 0.5);
  }
  
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 10px 5px rgba(77, 124, 255, 0.5);
  }
  
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(77, 124, 255, 0.5);
  }
}

/* 域名列表样式 */
.domain-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.domain-input-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
  padding: 16px;
  background: #f9fafc;
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.domain-input {
  padding: 10px 14px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  font-size: 14px;
  width: 100%;
  height: 40px;
  transition: all 0.2s;
}

.domain-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(77, 124, 255, 0.2);
  outline: none;
}

.input-buttons {
  display: flex;
  gap: 8px;
}

.domain-list-container {
  max-height: 400px;
  overflow-y: auto;
  margin-bottom: 15px;
}

.domain-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.domain-table th,
.domain-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.domain-table th {
  background: #f9fafc;
  font-weight: 600;
  color: #606266;
}

.domain-table tr:hover {
  background: rgba(77, 124, 255, 0.05);
}

.domain-actions {
  display: flex;
  gap: 8px;
}

.delete-btn {
  color: var(--error-color);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.delete-btn:hover {
  background: rgba(245, 108, 108, 0.1);
  text-decoration: none;
}

/* 空状态 */
.empty-state {
  padding: 30px;
  text-align: center;
  color: #909399;
  font-size: 14px;
}

/* 页脚 */
.footer {
  background: #f9fafc;
  padding: 16px;
  text-align: center;
  font-size: 13px;
  color: #909399;
  border-top: 1px solid var(--border-color);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
    gap: 16px;
  }
  
  .left-panel, .right-panel {
    flex: 1 0 100%;
  }
  
  .left-panel {
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }
} 