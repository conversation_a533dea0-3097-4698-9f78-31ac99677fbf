class CategoryDialog {
  constructor() {
    this.dialog = document.querySelector(".category-dialog");
    this.list = document.querySelector(".category-list");
    this.selectedText = document.querySelector(".selected-text");
    this.closeBtn = document.querySelector(".close-btn");

    this.selectedIndex = 0;
    this.categories = [];

    // 用于存储位置信息
    this.lastPosition = null;

    // 确保对话框使用固定定位
    if (this.dialog) {
      // 强制设置定位方式和z-index
      this.dialog.style.position = "fixed";
      this.dialog.style.zIndex = "2147483647"; // 最大z-index值，确保在最上层

      // 添加调试样式，便于排查问题
      this.dialog.dataset.positionDebug = "initialized";
    }

    this.initEvents();
  }

  initEvents() {
    // 监听来自父窗口的消息
    window.addEventListener("message", async (event) => {
      if (event.data.type === "show-dialog") {
        console.log("接收到显示对话框消息:", event.data);
        await this.show(event.data.text, event.data.position);
      }
    });

    // 键盘导航
    document.addEventListener("keydown", (e) => {
      switch (e.key) {
        case "ArrowUp":
          e.preventDefault();
          this.selectPrev();
          break;
        case "ArrowDown":
          e.preventDefault();
          this.selectNext();
          break;
        case "Enter":
          this.confirm();
          break;
        case "Escape":
          this.close();
          break;
      }
    });

    // 点击选择
    this.list.addEventListener("click", (e) => {
      const item = e.target.closest(".category-item");
      if (item) {
        const index = Array.from(this.list.children).indexOf(item);
        this.selectedIndex = index;
        this.updateSelection();
        this.confirm();
      }
    });

    // 点击外部关闭
    document.addEventListener("click", (e) => {
      if (this.dialog && !this.dialog.contains(e.target)) {
        this.close();
      }
    });

    // 添加关闭按钮点击事件
    if (this.closeBtn) {
      this.closeBtn.addEventListener("click", () => {
        this.close();
      });
    }

    // 监听窗口大小变化，重新计算位置
    window.addEventListener("resize", () => {
      if (
        this.dialog &&
        this.dialog.style.display === "block" &&
        this.lastPosition
      ) {
        this.positionDialog(this.lastPosition);
      }
    });

    // 监听滚动事件，重新计算位置（如果是absolute定位）
    window.addEventListener(
      "scroll",
      () => {
        if (
          this.dialog &&
          this.dialog.style.display === "block" &&
          this.lastPosition
        ) {
          const style = window.getComputedStyle(this.dialog);
          if (style.position === "absolute") {
            this.positionDialog(this.lastPosition);
          }
        }
      },
      { passive: true }
    );
  }

  async show(text, position) {
    try {
      if (!this.dialog) {
        console.error("对话框元素不存在");
        return;
      }

      console.log("显示对话框，文本:", text, "位置:", position);

      // 存储位置信息以便后续使用
      this.lastPosition = position;

      this.selectedText.textContent = text;

      // 获取分类列表
      const response = await chrome.runtime.sendMessage({
        opt: "rpc",
        func: "getKeywordsString2",
      });

      this.categories = response || [];

      // 渲染分类列表
      this.list.innerHTML = this.categories
        .map(
          (cat, i) => `
        <div class="category-item ${i === 0 ? "active" : ""}">
          ${cat.name || `分类 ${i + 1}`}
        </div>
      `
        )
        .join("");

      // 先使对话框可见但透明，以便获取真实尺寸
      this.dialog.style.display = "block";
      this.dialog.style.opacity = "0";

      // 更新选择状态
      this.selectedIndex = 0;
      this.updateSelection();

      // 延迟计算位置，确保DOM已完全更新
      setTimeout(() => {
        // 计算并设置对话框位置
        this.positionDialog(position);

        // 计算完位置后显示对话框
        this.dialog.style.opacity = "1";

        // 记录位置信息到DOM，便于调试
        this.dialog.dataset.positionApplied = JSON.stringify({
          left: this.dialog.style.left,
          top: this.dialog.style.top,
          time: new Date().toISOString(),
        });
      }, 50);
    } catch (error) {
      console.error("显示对话框失败:", error);
      if (typeof Utils !== "undefined" && Utils.handleError) {
        Utils.handleError(error, "show", "DOM");
      }
    }
  }

  /**
   * 计算并设置对话框位置
   * @param {Object} position - 位置信息对象，可能包含x,y或rect
   */
  positionDialog(position) {
    if (!this.dialog) return;

    // 获取弹窗尺寸
    this.dialog.style.display = "block";
    this.dialog.style.opacity = "0";
    const popupWidth = this.dialog.offsetWidth || 320;
    const popupHeight = this.dialog.offsetHeight || 180;

    let rect;
    if (position && position.rect) {
      rect = position.rect;
    } else if (
      position &&
      position.x !== undefined &&
      position.y !== undefined
    ) {
      // 构造一个虚拟rect
      rect = {
        left: position.x,
        right: position.x,
        top: position.y,
        bottom: position.y,
        width: 0,
        height: 0,
      };
    } else {
      // 默认居中
      rect = {
        left: window.innerWidth / 2 - popupWidth / 2,
        top: window.innerHeight / 2 - popupHeight / 2,
        bottom: window.innerHeight / 2 + popupHeight / 2,
        right: window.innerWidth / 2 + popupWidth / 2,
        width: popupWidth,
        height: popupHeight,
      };
    }

    const { left, top } = this.getPopupPosition(rect, popupWidth, popupHeight);

    this.dialog.style.position = "fixed";
    this.dialog.style.left = `${left}px`;
    this.dialog.style.top = `${top}px`;
    this.dialog.style.opacity = "1";

    // 显示后再次校正
    setTimeout(() => this.keepPopupInView(this.dialog), 0);
  }

  /**
   * 新增统一的弹窗定位工具函数
   * @param {Object} rect - 选区矩形
   * @param {number} popupWidth - 弹窗宽度
   * @param {number} popupHeight - 弹窗高度
   * @returns {Object} 计算后的left和top
   */
  getPopupPosition(rect, popupWidth, popupHeight) {
    let left = rect.left;
    let top = rect.bottom + 8;

    if (left + popupWidth > window.innerWidth - 10) {
      left = window.innerWidth - popupWidth - 10;
    }
    if (left < 10) left = 10;

    if (top + popupHeight > window.innerHeight - 10) {
      if (rect.top - popupHeight - 8 > 10) {
        top = rect.top - popupHeight - 8;
      } else {
        top = window.innerHeight - popupHeight - 10;
      }
    }
    if (top < 10) top = 10;

    return { left, top };
  }

  /**
   * 弹窗显示后再次校正
   * @param {Element} popup - 弹窗元素
   */
  keepPopupInView(popup) {
    const rect = popup.getBoundingClientRect();
    let left = rect.left,
      top = rect.top;
    let changed = false;

    if (rect.right > window.innerWidth - 10) {
      left = window.innerWidth - rect.width - 10;
      changed = true;
    }
    if (rect.left < 10) {
      left = 10;
      changed = true;
    }
    if (rect.bottom > window.innerHeight - 10) {
      top = window.innerHeight - rect.height - 10;
      changed = true;
    }
    if (rect.top < 10) {
      top = 10;
      changed = true;
    }
    if (changed) {
      popup.style.left = `${left}px`;
      popup.style.top = `${top}px`;
    }
  }

  /**
   * 从当前选区获取位置信息
   * @returns {Object|null} 位置信息对象
   */
  getSelectionPosition() {
    try {
      const selection = window.getSelection();
      if (!selection || !selection.rangeCount) return null;

      const range = selection.getRangeAt(0);
      const rect = range.getBoundingClientRect();

      // 只有当选区有实际尺寸时才返回
      if (rect.width > 0 || rect.height > 0) {
        return { rect };
      }

      return null;
    } catch (error) {
      console.error("获取选区位置失败:", error);
      return null;
    }
  }

  close() {
    try {
      // 先隐藏对话框
      if (this.dialog) {
        this.dialog.style.display = "none";
      }

      // 重置位置信息
      this.lastPosition = null;

      // 通知父页面关闭iframe
      if (window.parent) {
        window.parent.postMessage(
          {
            type: "close-dialog",
          },
          "*"
        );
      }
    } catch (error) {
      console.error("关闭对话框失败:", error);
    }
  }

  selectPrev() {
    if (this.selectedIndex > 0) {
      this.selectedIndex--;
      this.updateSelection();
    }
  }

  selectNext() {
    if (this.selectedIndex < this.categories.length - 1) {
      this.selectedIndex++;
      this.updateSelection();
    }
  }

  updateSelection() {
    const items = this.list.querySelectorAll(".category-item");
    items.forEach((item, i) => {
      item.classList.toggle("active", i === this.selectedIndex);
    });
  }

  async confirm() {
    try {
      const category = this.categories[this.selectedIndex];
      if (!category) return;

      const text = this.selectedText.textContent;

      // 添加到分类
      category.data = category.data ? `${category.data} ${text}` : text;

      // 保存更新
      await chrome.runtime.sendMessage({
        opt: "rpc",
        func: "setKeywordsString2",
        args: [this.categories],
      });

      // 通知内容页面刷新高亮
      await chrome.runtime.sendMessage({
        opt: "event",
        event: "reapplyHighlights",
      });

      this.close();
    } catch (error) {
      console.error("添加关键词失败:", error);
    }
  }
}

window.categoryDialog = new CategoryDialog();
