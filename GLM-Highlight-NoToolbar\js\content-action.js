// 全局变量
let dialogElement = null;
let closeHandler = null;

// 初始化全局变量
window.keywords = [];
window.tabActive = true;
window.highlighter = null;

// 初始化函数
async function initialize(retryCount = 0) {
  try {
    // 等待配置加载
    if (!window.HighlighterConfig) {
      if (retryCount < 5) {
        setTimeout(() => initialize(retryCount + 1), 100);
        return;
      }
      throw new Error("HighlighterConfig not loaded");
    }

    // 初始化高亮器
    if (!window.highlighter) {
      const { default: TextHighlighter } = await import("./highlighter.js");
      window.highlighter = new TextHighlighter(window.HighlighterConfig);
    }

    // 获取关键词
    const keywords = await chrome.runtime.sendMessage({
      opt: "rpc",
      func: "getKeywordsArray",
    });

    if (keywords && Array.isArray(keywords)) {
      window.keywords = keywords;
    }

    // 获取当前标签页状态
    const [tab] = await chrome.tabs.query({
      active: true,
      currentWindow: true,
    });

    if (tab) {
      const tabStatus = await chrome.runtime.sendMessage({
        opt: "rpc",
        func: "getTabActiveStatus",
        args: [tab.id],
      });

      window.tabActive = tabStatus !== false;

      if (window.tabActive && window.keywords.length > 0) {
        handleHighlight(document.body, window.keywords);
      }
    }

    console.log("GLM-Highlight 初始化完成:", {
      keywordsCount: window.keywords.length,
      tabActive: window.tabActive,
    });
  } catch (error) {
    Utils.handleError(error, "initialize", "RUNTIME");
    if (retryCount < 3) {
      setTimeout(() => initialize(retryCount + 1), 100);
    }
  }
}

// 统一的高亮处理
function handleHighlight(element, keywords, shouldClear = true) {
  if (!window.highlighter || !element) return;

  try {
    if (shouldClear) {
      window.highlighter.clearHighlight(element);
    }

    if (keywords?.length) {
      window.highlighter.highlight(element, keywords);
    }
  } catch (error) {
    Utils.handleError(error, "handleHighlight", "DOM");
  }
}

// 监听来自后台脚本的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (!Utils.verifyMessage(message, "event")) {
    return false;
  }

  if (message.opt === "event") {
    switch (message.event) {
      case "updateHighlightStatus":
        const shouldHighlight = message.shouldHighlight !== false;
        const tabStatus = message.tabStatus;
        const globalActive = message.globalActive;

        let finalStatus;
        if (tabStatus !== undefined) {
          finalStatus = tabStatus;
        } else if (globalActive !== undefined) {
          finalStatus = globalActive;
        } else {
          finalStatus = true;
        }

        window.tabActive = shouldHighlight && finalStatus;

        if (window.tabActive) {
          handleHighlight(document.body, window.keywords);
        } else {
          if (window.highlighter) {
            window.highlighter.clearHighlight(document.body);
          }
        }

        console.log("GLM-Highlight 状态更新:", {
          shouldHighlight,
          tabStatus,
          globalActive,
          finalStatus: window.tabActive,
        });
        break;

      case "reapplyHighlights":
        chrome.runtime
          .sendMessage({
            opt: "rpc",
            func: "getKeywordsArray",
          })
          .then((keywords) => {
            if (keywords && Array.isArray(keywords)) {
              window.keywords = keywords;
              if (window.tabActive) {
                handleHighlight(document.body, window.keywords);
              }
            }
          });
        break;

      case "keywordsUpdated":
        if (message.data && Array.isArray(message.data.keywords)) {
          const receivedKeywords = message.data.keywords;
          const currentTabId = message.data.currentTabId;

          const processKeywords = () => {
            if (message.data.categoryStatus && currentTabId) {
              const categoryStatus = message.data.categoryStatus;
              const filteredKeywords = [];

              receivedKeywords.forEach((keyword) => {
                const categoryKey = `${currentTabId}_${keyword.categoryIndex}`;
                const isEnabled = categoryStatus[categoryKey];

                if (isEnabled !== false) {
                  filteredKeywords.push(keyword);
                }
              });

              window.keywords = filteredKeywords;

              console.log("GLM-Highlight 关键词更新(标签页特定):", {
                originalCount: receivedKeywords.length,
                filteredCount: window.keywords.length,
                tabActive: window.tabActive,
                categoryStatus: categoryStatus,
                currentTabId: currentTabId,
              });

              if (window.tabActive) {
                handleHighlight(document.body, window.keywords);
              }
            } else {
              window.keywords = receivedKeywords;

              console.log("GLM-Highlight 关键词更新(无分类):", {
                keywordsCount: window.keywords.length,
                tabActive: window.tabActive,
              });

              if (window.tabActive) {
                handleHighlight(document.body, window.keywords);
              }
            }
          };

          processKeywords();
        } else {
          window.keywords = receivedKeywords;

          console.log("GLM-Highlight 关键词更新(无标签页ID):", {
            keywordsCount: window.keywords.length,
            tabActive: window.tabActive,
          });

          if (window.tabActive) {
            handleHighlight(document.body, window.keywords);
          }
        }
        break;

      case "urlActiveStatusChanged":
        const { domain, status, tabId } = message.data;
        console.log("收到URL状态变化:", { domain, status, tabId });

        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
          if (tabs.length > 0 && tabs[0].id === tabId) {
            window.tabActive = status;

            if (window.tabActive) {
              handleHighlight(document.body, window.keywords);
            } else {
              if (window.highlighter) {
                window.highlighter.clearHighlight(document.body);
              }
            }

            console.log("GLM-Highlight URL状态已更新:", {
              domain,
              status,
              tabActive: window.tabActive,
            });
          }
        });
        break;

      // 移除了 translatorToolbarSettingChanged 事件处理

      default:
        console.warn("未处理的事件类型:", message.event);
    }
    sendResponse({ success: true });
    return true;
  }

  // 处理 RPC 消息
  if (message.opt === "rpc") {
    sendResponse({ success: true });
    return true;
  }

  return false;
});

// 添加选择文本处理
async function handleSelection(e) {
  try {
    const text = window.getSelection().toString().trim();
    if (!text) return;

    // 获取选区位置
    const selection = window.getSelection();
    const range = selection.getRangeAt(selection.rangeCount - 1);
    const rect = range.getBoundingClientRect();

    // 计算弹窗位置
    const position = {
      x: Math.min(rect.left, window.innerWidth - 320),
      y: Math.min(rect.bottom + window.scrollY, window.innerHeight - 420),
    };

    // 获取分类列表
    const categories = await chrome.runtime.sendMessage({
      opt: "rpc",
      func: "getKeywordsString2",
    });

    // 如果已存在弹窗则移除
    if (dialogElement) {
      if (dialogElement) {
        dialogElement.remove();
      }
    }

    // 创建弹窗
    dialogElement = document.createElement("div");
    dialogElement.style.cssText = `
      position: fixed;
      left: ${position.x}px;
      top: ${position.y}px;
      background: white;
      border: 1px solid #ddd;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 2147483647;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      max-width: 300px;
      max-height: 400px;
      overflow-y: auto;
    `;

    // 添加标题
    const header = document.createElement("div");
    header.style.cssText = `
      padding: 12px 16px;
      border-bottom: 1px solid #eee;
      font-weight: 500;
      color: #333;
    `;
    header.textContent = `添加 "${text}" 到分类`;
    dialogElement.appendChild(header);

    // 添加分类选项
    categories.forEach((category, index) => {
      const item = document.createElement("div");
      item.style.cssText = `
        padding: 12px 16px;
        cursor: pointer;
        border-bottom: 1px solid #f5f5f5;
        display: flex;
        align-items: center;
        gap: 8px;
      `;

      // 颜色指示器
      const colorIndicator = document.createElement("div");
      colorIndicator.style.cssText = `
        width: 16px;
        height: 16px;
        border-radius: 3px;
        flex-shrink: 0;
      `;
      colorIndicator.className = `chrome-extension-mutihighlight-style-${category.colour}`;

      // 分类名称
      const nameSpan = document.createElement("span");
      nameSpan.textContent = category.name || `分类 ${index + 1}`;
      nameSpan.style.flex = "1";

      item.appendChild(colorIndicator);
      item.appendChild(nameSpan);

      // 悬停效果
      item.onmouseover = () => (item.style.backgroundColor = "#f5f7fa");
      item.onmouseout = () => (item.style.backgroundColor = "transparent");

      // 点击处理
      item.onclick = async () => {
        try {
          const words = new Set((category.data || "").trim().split(/\s+/));
          words.add(text);
          category.data = Array.from(words).join(" ");

          // 更新数据
          await chrome.runtime.sendMessage({
            opt: "rpc",
            func: "setKeywordsString2",
            args: [categories],
          });

          // 刷新高亮
          chrome.runtime.sendMessage({
            opt: "event",
            event: "reapplyHighlights",
          });

          // 关闭弹窗
          if (dialogElement) {
            dialogElement.remove();
          }
          dialogElement = null;
        } catch (error) {
          console.error("添加高亮失败:", error);
        }
      };

      dialogElement.appendChild(item);
    });

    // 添加到页面
    document.body.appendChild(dialogElement);

    // 点击其他区域关闭弹窗
    closeHandler = (e) => {
      if (!dialogElement?.contains(e.target)) {
        if (dialogElement) {
          dialogElement.remove();
        }
        dialogElement = null;
        document.removeEventListener("mousedown", closeHandler);
        closeHandler = null;
      }
    };
    document.addEventListener("mousedown", closeHandler);
  } catch (error) {
    console.error("处理选择文本失败:", error);
  }
}

// 只处理快捷键和右键菜单消息
chrome.runtime.onMessage.addListener((message) => {
  if (message.type === "add-to-category") {
    handleSelection();
  }
});

// 启动初始化
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", initialize);
} else {
  initialize();
}
