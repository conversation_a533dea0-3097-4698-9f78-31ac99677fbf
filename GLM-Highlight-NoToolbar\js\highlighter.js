// 核心高亮类
class TextHighlighter {
  constructor(options = {}) {
    // 使用统一配置
    this.config = {
      debug: false, // debug设置默认值为 false
      ...window.HighlighterConfig,
      ...options,
    };

    // 合并选项
    this.options = {
      caseSensitive: true, // 修改为 true，支持大小写敏感
      className: this.config.className,
      stylePrefix: this.config.stylePrefix,
      batchSize: this.config.performance.batch.size,
      ...options,
    };

    // 实现 LRU 缓存
    this.MAX_PATTERN_CACHE = this.config.performance.batch.maxNodes || 1000;
    this.nodeStates = new WeakMap();
    this.patternCache = new Map();
    this.patternCacheOrder = [];

    // 初始化 DOM 片段缓存
    this.fragmentCache = new WeakMap();

    // 添加缓存清理定时器引用
    this.cacheCleanupTimer = null;

    // 添加关键词缓存
    this._keywordCache = null;

    // 【新增】性能统计
    this.performanceStats = {
      totalProcessed: 0,
      totalSkipped: 0,
      totalMatches: 0,
      averageProcessingTime: 0,
      cacheHitRate: 0,
      lastResetTime: Date.now(),
    };

    // 初始化缓存清理
    this._setupCacheCleanup();
  }

  _setupCacheCleanup() {
    // 清理旧定时器
    if (this.cacheCleanupTimer) {
      clearInterval(this.cacheCleanupTimer);
    }

    // 使用简化缓存清理策略
    const cleanupCache = () => {
      try {
        // 简单清理：超过阈值就清空缓存
        if (this.patternCache.size > this.MAX_PATTERN_CACHE) {
          this.patternCache.clear();
        }

        // 记录缓存统计
        if (this.config.debug) {
          Utils.handleError(
            `缓存状态: 模式缓存 ${this.patternCache.size}/${this.MAX_PATTERN_CACHE}`,
            "cacheCleanup",
            "LOG"
          );
        }
      } catch (error) {
        Utils.handleError(error, "cacheCleanup", "CACHE");
      }
    };

    // 定期清理 (每60秒)
    this.cacheCleanupTimer = setInterval(cleanupCache, 60000);

    // 添加页面卸载时的清理
    window.addEventListener("unload", () => this.destroyCacheTimer(), {
      once: true,
    });
  }

  // 添加清理缓存定时器的方法
  destroyCacheTimer() {
    if (this.cacheCleanupTimer) {
      clearInterval(this.cacheCleanupTimer);
      this.cacheCleanupTimer = null;
    }
  }

  clearCache() {
    this.nodeStates = new WeakMap();
    this.patternCache.clear();
    this.patternCacheOrder = [];
    this.fragmentCache = new WeakMap();
    this._keywordCache = null; // 清除关键词缓存
  }

  // 修改文本节点合并逻辑
  _mergeTextNodes(node) {
    if (!node || !node.parentNode) return;

    // 处理前后文本节点
    if (
      node.previousSibling &&
      node.previousSibling.nodeType === Node.TEXT_NODE
    ) {
      node.previousSibling.nodeValue =
        node.previousSibling.nodeValue + (node.nodeValue || node.textContent);
      if (node.nextSibling && node.nextSibling.nodeType === Node.TEXT_NODE) {
        node.previousSibling.nodeValue += node.nextSibling.nodeValue;
        node.nextSibling.remove();
      }
      node.remove();
    } else if (
      node.nextSibling &&
      node.nextSibling.nodeType === Node.TEXT_NODE
    ) {
      node.nextSibling.nodeValue =
        (node.nodeValue || node.textContent) + node.nextSibling.nodeValue;
      node.remove();
    }
  }

  // 优化批处理机制
  processBatch(nodes, processor) {
    let processed = 0;
    const processNextBatch = () => {
      const batchSize = this.config.performance.batch.size;
      const end = Math.min(processed + batchSize, nodes.length);

      for (let i = processed; i < end; i++) {
        processor(nodes[i]);
      }

      processed = end;

      if (processed < nodes.length) {
        requestAnimationFrame(processNextBatch);
      }
    };

    if (nodes.length > 0) {
      processNextBatch();
    }
  }

  // 统一的节点检查（增强版，支持 filter）
  shouldSkipNode(node, keyword = null, totalCounter = 0, counter = 0) {
    // 1. filter 回调判断
    if (typeof this.config.filter === "function") {
      // filter(node, term, totalCounter, counter)
      if (this.config.filter(node, keyword, totalCounter, counter) === false) {
        if (this.config.debug) {
          Utils.handleError(
            `[Highlighter] 节点被 filter 排除: ${keyword}`,
            "shouldSkipNode",
            "LOG"
          );
        }
        return true;
      }
    }

    // 2. 原有的跳过逻辑
    return Utils.dom.shouldSkipNode(node, this.config);
  }

  // 高亮主函数
  highlight(node, keywords) {
    if (!node || !keywords?.length) return;

    try {
      // 一次性收集所有文本节点
      const textNodes = this._collectTextNodes(node);

      // 批量处理节点但没有分片
      textNodes.forEach((textNode) => {
        this._processTextNode(textNode, keywords);
      });
    } catch (error) {
      Utils.handleError(error, "highlight", "PROCESS");
    }
  }

  // 优化的文本节点收集
  _collectTextNodes(container) {
    if (!container || !(container instanceof Node)) return [];

    const textNodes = [];
    const walker = document.createTreeWalker(container, NodeFilter.SHOW_TEXT, {
      acceptNode: (node) => {
        // 只传递 node，keyword/计数可选
        if (this.shouldSkipNode(node)) {
          return NodeFilter.FILTER_REJECT;
        }
        return NodeFilter.FILTER_ACCEPT;
      },
    });

    let node;
    while ((node = walker.nextNode())) {
      textNodes.push(node);
    }

    return textNodes;
  }

  _processTextNodesBatch(nodes) {
    // 批量处理文本节点
    nodes.forEach((node) => {
      if (!this.nodeStates.has(node)) {
        this.nodeStates.set(node, true);
      }
    });
  }

  // 预处理关键词
  _preprocessKeywords(keywords) {
    return keywords
      .filter(
        (item) =>
          item?.words && typeof item.words === "string" && item.words.trim()
      )
      .map((item) => {
        const processed = {
          ...item,
          words: item.words.trim(),
          // 使用 WeakRef 包装正则对象
          pattern: new WeakRef(this._getSearchPattern(item.words)),
          length: item.words.length,
        };
        return processed;
      })
      .sort((a, b) => b.length - a.length);
  }

  // 获取或创建搜索正则
  _getSearchPattern(keyword) {
    if (!keyword) return null;

    // 使用关键词和大小写选项作为缓存键
    const cacheKey = `${keyword}_${this.options.caseSensitive}`;
    if (this.patternCache.has(cacheKey)) {
      // 更新 LRU 顺序
      const index = this.patternCacheOrder.indexOf(cacheKey);
      if (index > -1) {
        this.patternCacheOrder.splice(index, 1);
      }
      this.patternCacheOrder.push(cacheKey);
      return this.patternCache.get(cacheKey);
    }

    // 转义特殊字符
    const escapedKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");

    // 根据配置决定是否大小写敏感
    const flags = this.options.caseSensitive ? "g" : "gi";
    const pattern = new RegExp(`(?:${escapedKeyword})`, flags);

    // 使用 LRU 缓存策略
    if (this.patternCache.size >= this.MAX_PATTERN_CACHE) {
      const oldestKey = this.patternCacheOrder.shift();
      this.patternCache.delete(oldestKey);
    }

    this.patternCache.set(cacheKey, pattern);
    this.patternCacheOrder.push(cacheKey);
    return pattern;
  }

  // 【简化版本】文本节点处理，先确保基本功能正常
  _processTextNode(node, keywords) {
    try {
      if (!Utils.dom.isTextNode(node) || !keywords?.length) return;

      const text = node.textContent;
      // 跳过过短或空白文本
      if (!text || !text.trim()) return;

      // 检查父节点
      const parentElement = node.parentElement;
      if (!parentElement || this.shouldSkipNode(parentElement)) return;

      // 超长文本特殊处理 - 降低阈值提升响应性
      if (text.length > 3000) {
        // 分块处理超长文本
        this._processLongTextNode(node, keywords);
        return;
      }

      // 创建匹配结果数组
      const matches = this._findMatches(text, keywords);

      // 如果没有匹配项，直接返回
      if (matches.length === 0) return;

      // 使用Range API选择整个文本节点
      const range = document.createRange();
      range.selectNodeContents(node);

      // 使用文档片段一次性构建所有高亮
      const fragment = this._createOptimizedFragment(text, matches);

      // 一次性替换整个节点内容
      range.deleteContents();
      range.insertNode(fragment);

      // 更新性能统计
      if (this.performanceStats) {
        this.performanceStats.totalProcessed++;
        this.performanceStats.totalMatches += matches.length;
      }
    } catch (error) {
      Utils.handleError(error, "processTextNode", "HIGHLIGHT");
    }
  }

  // 分块处理超长文本
  _processLongTextNode(node, keywords, chunkSize = 5000) {
    try {
      const text = node.textContent;
      const totalChunks = Math.ceil(text.length / chunkSize);

      // 创建一个文档片段存储所有结果
      const fragment = document.createDocumentFragment();

      // 处理每个块
      for (let i = 0; i < totalChunks; i++) {
        const start = i * chunkSize;
        const end = Math.min((i + 1) * chunkSize, text.length);
        const chunkText = text.substring(start, end);

        // 查找当前块中的匹配
        const matches = this._findMatches(chunkText, keywords);

        // 如果没有匹配项，直接添加文本
        if (matches.length === 0) {
          fragment.appendChild(document.createTextNode(chunkText));
          continue;
        }

        // 创建高亮片段
        const chunkFragment = this._createOptimizedFragment(chunkText, matches);
        fragment.appendChild(chunkFragment);
      }

      // 替换原始节点
      const range = document.createRange();
      range.selectNodeContents(node);
      range.deleteContents();
      range.insertNode(fragment);
    } catch (error) {
      Utils.handleError(error, "processLongTextNode", "HIGHLIGHT");
    }
  }

  // 优化查找匹配方法 - 批量重叠处理
  _findMatches(text, keywords) {
    try {
      // 第一阶段：快速收集所有匹配（无重叠检查）
      const allMatches = [];
      const sortedKeywords = this._prepareKeywords(keywords);

      // 查找所有匹配
      for (const keyword of sortedKeywords) {
        const keywordText = keyword.words || keyword;
        // 跳过空关键词，但允许单字符关键词
        if (!keywordText) continue;

        // 使用优化的搜索方法找出所有匹配位置
        const positions = this._findAllOccurrences(text, keywordText);

        // 快速收集所有匹配
        for (const index of positions) {
          allMatches.push({
            start: index,
            end: index + keywordText.length,
            text: text.slice(index, index + keywordText.length),
            keyword: keyword,
          });
        }
      }

      // 第二阶段：批量处理重叠（O(n log n)）
      return this._batchResolveOverlaps(allMatches);
    } catch (error) {
      Utils.handleError(error, "findMatches", "HIGHLIGHT");
      return [];
    }
  }

  // 高效查找字符串中所有出现位置
  _findAllOccurrences(text, pattern) {
    const positions = [];
    let index = -1;
    const maxMatches = 100; // 安全限制

    // 对于短模式，使用优化的 indexOf
    if (pattern.length <= 3) {
      while ((index = text.indexOf(pattern, index + 1)) !== -1) {
        positions.push(index);
        if (positions.length >= maxMatches) break;
      }
    } else {
      // 对于长模式，使用 KMP 算法
      const lps = this._computeLPSArray(pattern);
      let i = 0; // text 索引
      let j = 0; // pattern 索引

      while (i < text.length) {
        if (pattern[j] === text[i]) {
          i++;
          j++;
        }

        if (j === pattern.length) {
          positions.push(i - j);
          j = lps[j - 1];

          if (positions.length >= maxMatches) break;
        } else if (i < text.length && pattern[j] !== text[i]) {
          if (j !== 0) {
            j = lps[j - 1];
          } else {
            i++;
          }
        }
      }
    }

    return positions;
  }

  // KMP 算法的部分匹配表计算
  _computeLPSArray(pattern) {
    const lps = new Array(pattern.length).fill(0);
    let len = 0;
    let i = 1;

    while (i < pattern.length) {
      if (pattern[i] === pattern[len]) {
        len++;
        lps[i] = len;
        i++;
      } else {
        if (len !== 0) {
          len = lps[len - 1];
        } else {
          lps[i] = 0;
          i++;
        }
      }
    }

    return lps;
  }

  // 批量重叠处理方法
  _batchResolveOverlaps(matches) {
    if (matches.length === 0) return [];

    // 按起始位置排序
    matches.sort((a, b) => a.start - b.start);

    const result = [];
    let lastEnd = -1;

    for (const match of matches) {
      if (match.start >= lastEnd) {
        result.push(match);
        lastEnd = match.end;
      }
    }

    return result;
  }

  // 保留原有的插入排序区间方法（向后兼容）
  _insertSortedRange(ranges, start, end) {
    // 二分查找插入位置
    let low = 0;
    let high = ranges.length - 1;

    while (low <= high) {
      const mid = Math.floor((low + high) / 2);
      if (ranges[mid][0] < start) {
        low = mid + 1;
      } else {
        high = mid - 1;
      }
    }

    // 直接在正确位置插入
    ranges.splice(low, 0, [start, end]);
  }

  // 【新增】轻量级关键词版本计算
  _getKeywordVersion(keywords) {
    if (!keywords || keywords.length === 0) return 0;

    // 基于关键词数量和总长度生成简单版本号
    let totalLength = 0;
    let hashSum = 0;

    for (const keyword of keywords) {
      const word = keyword.words || keyword;
      totalLength += word.length;
      // 简单哈希：字符码累加
      for (let i = 0; i < word.length; i++) {
        hashSum += word.charCodeAt(i);
      }
    }

    return `${keywords.length}_${totalLength}_${hashSum}`;
  }

  // 【新增】智能关键词预过滤
  _prefilterKeywords(text, keywords) {
    if (!text || !keywords || keywords.length === 0) return [];

    // 转换为小写进行快速检查（不影响原始匹配的大小写敏感性）
    const lowerText = text.toLowerCase();
    const candidateKeywords = [];

    for (const keyword of keywords) {
      const word = keyword.words || keyword;
      if (!word) continue;

      // 快速预检查：文本中是否包含关键词的首字符
      const firstChar = word.charAt(0).toLowerCase();
      if (lowerText.includes(firstChar)) {
        // 进一步检查：是否包含关键词的前几个字符
        const prefix =
          word.length > 2
            ? word.substring(0, 3).toLowerCase()
            : word.toLowerCase();
        if (lowerText.includes(prefix)) {
          candidateKeywords.push(keyword);
        }
      }
    }

    if (this.config.debug && candidateKeywords.length < keywords.length) {
      console.log(
        `[Highlighter] 预过滤: ${keywords.length} -> ${candidateKeywords.length} 关键词`
      );
    }

    return candidateKeywords;
  }

  // 预处理和缓存关键词
  _prepareKeywords(keywords) {
    // 创建唯一缓存键
    const cacheKey = keywords
      .map((k) => (typeof k === "object" ? k.words : k))
      .join("|");

    // 检查缓存
    if (this._keywordCache && this._keywordCache.key === cacheKey) {
      return this._keywordCache.sorted;
    }

    // 排序关键词
    const sortedKeywords = [...keywords].sort((a, b) => {
      const lenA = (a.words || a).length;
      const lenB = (b.words || b).length;
      return lenB - lenA; // 长度降序排列
    });

    // 缓存结果
    this._keywordCache = {
      key: cacheKey,
      sorted: sortedKeywords,
    };

    return sortedKeywords;
  }

  // 优化的重叠检查
  _hasOverlap(ranges, start, end) {
    // 二分查找优化的重叠检查
    let left = 0;
    let right = ranges.length - 1;

    while (left <= right) {
      const mid = Math.floor((left + right) / 2);
      const [rangeStart, rangeEnd] = ranges[mid];

      if (end <= rangeStart) {
        right = mid - 1;
      } else if (start >= rangeEnd) {
        left = mid + 1;
      } else {
        return true; // 找到重叠
      }
    }

    return false;
  }

  // 优化的节点过滤
  _shouldSkipNode(node) {
    // 使用相同的检查逻辑
    return this.shouldSkipNode(node);
  }

  // 清理空的高亮 span 标签
  _cleanEmptySpans(container) {
    if (!container) return;

    const spans = Array.from(
      container.getElementsByClassName(this.config.className)
    );

    this.processBatch(spans, (span) => {
      if (!span.textContent.trim()) {
        span.remove();
      }
    });
  }

  // 优化后的清理高亮方法
  clearHighlight(node) {
    try {
      if (!node) return;

      // 获取所有高亮元素
      const highlights = Array.from(
        node.getElementsByClassName(this.config.className)
      );

      // 使用批处理清理高亮
      for (const highlight of highlights) {
        if (!highlight || !highlight.parentNode) continue;

        try {
          // 获取高亮文本
          const text = highlight.textContent;

          // 处理相邻文本节点
          if (
            highlight.previousSibling?.nodeType === Node.TEXT_NODE &&
            highlight.nextSibling?.nodeType === Node.TEXT_NODE
          ) {
            // 合并前后文本节点
            highlight.previousSibling.nodeValue =
              highlight.previousSibling.nodeValue +
              text +
              highlight.nextSibling.nodeValue;
            highlight.nextSibling.remove();
          } else if (highlight.previousSibling?.nodeType === Node.TEXT_NODE) {
            // 合并前面的文本节点
            highlight.previousSibling.nodeValue += text;
          } else if (highlight.nextSibling?.nodeType === Node.TEXT_NODE) {
            // 合并后面的文本节点
            highlight.nextSibling.nodeValue =
              text + highlight.nextSibling.nodeValue;
          } else {
            // 创建新的文本节点
            const textNode = document.createTextNode(text);
            highlight.parentNode.insertBefore(textNode, highlight);
          }

          // 移除高亮元素
          highlight.remove();
        } catch (error) {
          console.error("清理高亮元素失败:", error, highlight);
          // 简单替换方案作为备选
          if (highlight.parentNode) {
            highlight.outerHTML = highlight.innerHTML;
          }
        }
      }

      // 清理空的 span 标签
      this._cleanEmptySpans(node);

      // 清理缓存
      this.clearCache();
    } catch (error) {
      console.error("清理高亮失败:", error);
    }
  }

  // 修改清理高亮状态的方法
  cleanHighlightState(node) {
    try {
      if (!node || !node.parentNode) return;

      // 1. 收集相邻文本节点
      const prevNode = node.previousSibling;
      const nextNode = node.nextSibling;
      const text = node.textContent;

      // 2. 直接修改文本内容
      if (
        prevNode?.nodeType === Node.TEXT_NODE &&
        nextNode?.nodeType === Node.TEXT_NODE
      ) {
        // 合并相邻文本节点
        prevNode.nodeValue = prevNode.nodeValue + text + nextNode.nodeValue;
        nextNode.remove();
      } else if (prevNode?.nodeType === Node.TEXT_NODE) {
        prevNode.nodeValue = prevNode.nodeValue + text;
      } else if (nextNode?.nodeType === Node.TEXT_NODE) {
        nextNode.nodeValue = text + nextNode.nodeValue;
      } else {
        // 创建新文本节点
        const textNode = document.createTextNode(text);
        node.parentNode.insertBefore(textNode, node);
      }

      // 3. 移除高亮节点
      node.remove();
    } catch (error) {
      console.error("清理高亮状态失败:", error);
    }
  }

  // 【新增】优化的文档片段创建方法 - 批量构建所有高亮
  _createOptimizedFragment(text, matches) {
    const fragment = document.createDocumentFragment();
    let lastIndex = 0;

    // 按开始位置排序（从前向后）
    matches.sort((a, b) => a.start - b.start);

    for (const match of matches) {
      // 添加未匹配文本
      if (match.start > lastIndex) {
        fragment.appendChild(
          document.createTextNode(text.slice(lastIndex, match.start))
        );
      }

      // 创建高亮元素
      const highlight = document.createElement("span");
      highlight.className = `${this.config.className} ${
        this.config.stylePrefix
      }${
        typeof match.keyword === "object"
          ? match.keyword.colour
          : this._getColorIndex(match.keyword)
      }`;
      highlight.textContent = match.text;
      highlight.style.fontStyle = "normal";
      fragment.appendChild(highlight);

      lastIndex = match.end;
    }

    // 添加剩余文本
    if (lastIndex < text.length) {
      fragment.appendChild(document.createTextNode(text.slice(lastIndex)));
    }

    return fragment;
  }

  // 获取颜色索引
  _getColorIndex(keyword) {
    let index = this.patternCache.get(keyword);
    if (!index) {
      index = (this.patternCache.size % 20) + 1;
      this.patternCache.set(keyword, index);
    }
    return index;
  }

  // 【新增】性能统计更新
  _updatePerformanceStats(operation, data) {
    const stats = this.performanceStats;

    switch (operation) {
      case "processTextNode":
        stats.totalProcessed++;
        stats.totalMatches += data.matchCount || 0;
        break;
      case "cacheHit":
        stats.cacheHitRate = stats.cacheHitRate * 0.9 + (data.hit ? 0.1 : 0);
        break;
    }
  }

  // 【新增】获取性能报告
  getPerformanceReport() {
    const stats = this.performanceStats;
    const runtime = Date.now() - stats.lastResetTime;

    return {
      totalProcessed: stats.totalProcessed,
      totalSkipped: stats.totalSkipped,
      totalMatches: stats.totalMatches,
      skipRate:
        stats.totalProcessed > 0
          ? (
              (stats.totalSkipped /
                (stats.totalProcessed + stats.totalSkipped)) *
              100
            ).toFixed(2) + "%"
          : "0%",
      cacheHitRate: (stats.cacheHitRate * 100).toFixed(2) + "%",
      averageMatchesPerNode:
        stats.totalProcessed > 0
          ? (stats.totalMatches / stats.totalProcessed).toFixed(2)
          : "0",
      runtime: (runtime / 1000).toFixed(2) + "s",
      cacheSize: this.patternCache.size,
      nodeStatesSize: "WeakMap (auto-managed)",
    };
  }

  // 【新增】重置性能统计
  resetPerformanceStats() {
    this.performanceStats = {
      totalProcessed: 0,
      totalSkipped: 0,
      totalMatches: 0,
      averageProcessingTime: 0,
      cacheHitRate: 0,
      lastResetTime: Date.now(),
    };
  }
}

// 创建单例实例前添加全局清理
if (window.highlighter && window.highlighter.destroyCacheTimer) {
  window.highlighter.destroyCacheTimer();
}

// 创建单例实例
window.highlighter = new TextHighlighter();
