<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>使用说明</title>
    <link rel="stylesheet" href="css/element-plus.css" />
    <style>
      body {
        margin: 0;
        padding: 24px;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background: #f5f7fa;
      }

      .help-dialog {
        width: 680px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        margin: 0 auto;
      }

      .help-header {
        padding: 20px 24px;
        border-bottom: 1px solid #ebeef5;
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .help-title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
        margin: 0;
      }

      .help-content {
        padding: 24px;
        font-size: 14px;
        line-height: 1.6;
        color: #606266;
      }

      .help-section {
        margin-bottom: 32px;
        padding: 20px;
        background: #f8f9fb;
        border-radius: 8px;
        border-left: 4px solid #409eff;
      }

      .help-section:last-child {
        margin-bottom: 0;
      }

      .section-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;
      }

      .section-icon {
        width: 24px;
        height: 24px;
        color: #409eff;
      }

      .help-section h3 {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin: 0;
      }

      .help-section ul {
        margin: 0;
        padding-left: 0;
      }

      .help-section > ul > li {
        margin: 8px 0;
        list-style-type: none;
        position: relative;
        padding-left: 20px;
      }

      .help-section > ul > li::before {
        content: "•";
        position: absolute;
        left: 0;
        color: #409eff;
      }

      .help-section ul ul {
        margin: 8px 0;
        padding-left: 20px;
      }

      .help-section ul ul li {
        margin: 4px 0;
        list-style-type: none;
        position: relative;
        padding-left: 16px;
      }

      .help-section ul ul li::before {
        content: "◦";
        position: absolute;
        left: 0;
        color: #909399;
      }

      .help-section li::after {
        content: none;
      }

      .help-footer {
        padding: 16px 24px;
        border-top: 1px solid #ebeef5;
        text-align: right;
      }

      .close-btn {
        padding: 8px 20px;
        background: #409eff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s;
      }

      .close-btn:hover {
        background: #66b1ff;
        transform: translateY(-1px);
      }

      .keyboard-shortcut {
        display: inline-block;
        padding: 2px 6px;
        background: #f0f2f5;
        border-radius: 4px;
        color: #606266;
        font-family: monospace;
        margin: 0 2px;
      }

      .shortcut-list {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .shortcut-list li {
        margin: 8px 0;
        position: relative;
      }

      .shortcut-note {
        font-size: 12px;
        color: #909399;
        margin-top: 4px;
        padding-left: 0;
      }
    </style>
  </head>
  <body>
    <div class="help-dialog">
      <div class="help-header">
        <svg class="section-icon" viewBox="0 0 1024 1024">
          <path
            fill="currentColor"
            d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
          />
          <path
            fill="currentColor"
            d="M512 336c-38.6 0-70 31.4-70 70v186c0 38.6 31.4 70 70 70s70-31.4 70-70V406c0-38.6-31.4-70-70-70z"
          />
          <circle fill="currentColor" cx="512" cy="236" r="70" />
        </svg>
        <h2 class="help-title">使用说明</h2>
      </div>
      <div class="help-content">
        <div class="help-section">
          <div class="section-header">
            <svg class="section-icon" viewBox="0 0 1024 1024">
              <path
                fill="currentColor"
                d="M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56c10.1-8.6 13.8-22.6 9.3-35.2l-0.9-2.6c-18.1-50.5-44.9-96.9-79.7-137.9l-1.8-2.1c-8.6-10.1-22.5-13.9-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85c-2.4-13.1-12.7-23.3-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5c-13.1 2.4-23.4 12.6-25.8 25.7l-15.8 85.4c-35.9 13.6-69.2 32.9-99 57.4l-81.9-29.1c-12.5-4.4-26.5-.7-35.1 9.5l-1.8 2.1c-34.8 41.1-61.6 87.5-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5c-10.1 8.6-13.8 22.6-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1c8.6 10.1 22.5 13.9 35.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4c2.4 13.1 12.7 23.3 25.8 25.7l2.7.5c26.1 4.7 52.8 7.1 79.5 7.1 26.7 0 53.5-2.4 79.5-7.1l2.7-.5c13.1-2.4 23.4-12.6 25.8-25.7l15.7-85c36.2-13.6 69.7-32.9 99.7-57.6l81.3 28.9c12.5 4.4 26.5.7 35.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9c-11.3 26.1-25.6 50.7-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97c-28.1 3.2-56.8 3.2-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2C570 602.3 541.9 614 512 614c-29.9 0-58-11.7-79.2-32.8C411.7 560 400 531.9 400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8C612.3 444 624 472.1 624 502c0 29.9-11.7 58-32.8 79.2z"
              />
            </svg>
            <h3>基本功能</h3>
          </div>
          <ul>
            <li>创建分类: 点击"创建"按钮添加新的高亮分类</li>
            <li>编辑分类: 可以修改分类名称和高亮关键词</li>
            <li>删除分类: 点击分类右上角的删除按钮</li>
            <li>开关分类: 使用分类右上角的开关控制该分类是否生效</li>
            <li>修改颜色: 点击色块可以更换该分类的高亮颜色</li>
            <li>拖拽排序: 按住分类边框可以拖动调整分类顺序</li>
          </ul>
        </div>
        <div class="help-section">
          <div class="section-header">
            <svg class="section-icon" viewBox="0 0 1024 1024">
              <path
                fill="currentColor"
                d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 608H136V232h752v536z"
              />
              <path
                fill="currentColor"
                d="M228 420h-48c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v48c0 4.4-3.6 8-8 8zm140 0h-48c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v48c0 4.4-3.6 8-8 8zm140 0h-48c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v48c0 4.4-3.6 8-8 8zm140 0h-48c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v48c0 4.4-3.6 8-8 8zm140 0h-48c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v48c0 4.4-3.6 8-8 8z"
              />
              <path
                fill="currentColor"
                d="M228 564h-48c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v48c0 4.4-3.6 8-8 8zm140 0h-48c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v48c0 4.4-3.6 8-8 8zm140 0h-48c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v48c0 4.4-3.6 8-8 8zm140 0h-48c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v48c0 4.4-3.6 8-8 8zm140 0h-48c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v48c0 4.4-3.6 8-8 8z"
              />
              <path
                fill="currentColor"
                d="M228 708h-48c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v48c0 4.4-3.6 8-8 8zm140 0h-48c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v48c0 4.4-3.6 8-8 8zm140 0h-48c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v48c0 4.4-3.6 8-8 8zm140 0h-48c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v48c0 4.4-3.6 8-8 8zm140 0h-48c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v48c0 4.4-3.6 8-8 8z"
              />
            </svg>
            <h3>快捷键操作</h3>
          </div>
          <ul class="shortcut-list">
            <li>
              添加高亮: 选中文本后按 Alt + Shift + C (Mac ^ + ⇧ + C)
              <div class="shortcut-note">
                如需修改快捷键，请在浏览器的扩展管理中心
                (chrome://extensions/shortcuts) 进行设置
              </div>
            </li>
            <li>删除高亮: 选中文本后按 Alt + Shift + D (Mac ^ + ⇧ + D)</li>
          </ul>
        </div>
        <div class="help-section">
          <div class="section-header">
            <svg class="section-icon" viewBox="0 0 1024 1024">
              <path
                fill="currentColor"
                d="M924.8 385.6a446.7 446.7 0 0 0-96-142.4 446.7 446.7 0 0 0-142.4-96C631.1 123.8 572.5 112 512 112s-119.1 11.8-174.4 35.2a446.7 446.7 0 0 0-142.4 96 446.7 446.7 0 0 0-96 142.4C75.8 440.9 64 499.5 64 560c0 132.7 58.3 257.7 159.9 343.1l1.7 1.4c5.8 4.8 13.1 7.5 20.6 7.5h531.7c7.5 0 14.8-2.7 20.6-7.5l1.7-1.4C901.7 817.7 960 692.7 960 560c0-60.5-11.9-119.1-35.2-174.4zM761.4 836H262.6A371.12 371.12 0 0 1 140 560c0-99.4 38.7-192.8 109-263 70.3-70.3 163.7-109 263-109 99.4 0 192.8 38.7 263 109 70.3 70.3 109 163.7 109 263 0 105.6-44.5 205.5-122.6 276z"
              />
              <path
                fill="currentColor"
                d="M623.5 421.5a8.03 8.03 0 0 0-11.3 0L527.7 506c-18.7-5-39.4-.2-54.1 14.5a55.95 55.95 0 0 0 0 79.2 55.95 55.95 0 0 0 79.2 0 55.87 55.87 0 0 0 14.5-54.1l84.5-84.5c3.1-3.1 3.1-8.2 0-11.3l-28.3-28.3zM490 320h44c4.4 0 8-3.6 8-8v-80c0-4.4-3.6-8-8-8h-44c-4.4 0-8 3.6-8 8v80c0 4.4 3.6 8 8 8z"
              />
            </svg>
            <h3>右键菜单</h3>
          </div>
          <ul>
            <li>
              添加高亮:
              选中文本后右键选择"添加到高亮分类"，在弹出的分类列表中选择目标分类
            </li>
            <li>
              删除高亮:
              选中已高亮的文本后右键选择"删除高亮"，即可移除该关键词的高亮效果
            </li>
          </ul>
        </div>
        <div class="help-section">
          <div class="section-header">
            <svg class="section-icon" viewBox="0 0 1024 1024">
              <path
                fill="currentColor"
                d="M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"
              />
              <path
                fill="currentColor"
                d="M492 400h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H492c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8zM492 544h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H492c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8zM492 688h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H492c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8zM348 400h48c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8zM348 544h48c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8zM348 688h48c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"
              />
            </svg>
            <h3>分类管理</h3>
          </div>
          <ul>
            <li>编辑分类名称: 在管理界面点击分类名称即可修改</li>
            <li>删除分类: 点击分类右侧的删除按钮可以删除整个分类</li>
            <li>
              编辑关键词: 在分类的文本框中直接编辑关键词，多个关键词用空格分隔
            </li>
            <li>修改颜色: 点击颜色块可以打开颜色选择器，选择新的高亮颜色</li>
          </ul>
        </div>
        <div class="help-section">
          <div class="section-header">
            <svg class="section-icon" viewBox="0 0 1024 1024">
              <path
                fill="currentColor"
                d="M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"
              />
              <path
                fill="currentColor"
                d="M492 400h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H492c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8zM492 544h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H492c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8zM492 688h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H492c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8zM348 400h48c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8zM348 544h48c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8zM348 688h48c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"
              />
            </svg>
            <h3>标签页独立控制</h3>
          </div>
          <ul>
            <li>
              独立的开关状态:
              <ul>
                <li>每个标签页（网站）拥有独立的高亮开关状态，互不影响</li>
                <li>在一个标签页关闭高亮后，其他标签页的高亮状态不会改变</li>
                <li>标签页状态会被保存，即使浏览器重启后也会保持</li>
              </ul>
            </li>
            <li>
              黑白名单机制:
              <ul>
                <li>
                  可在浏览器右上角插件图标点击右键菜单，选择"网站高亮设置"
                </li>
                <li>黑名单模式: 除列表中的网站外，其他网站默认启用高亮</li>
                <li>白名单模式: 仅列表中的网站启用高亮，其他网站默认禁用</li>
                <li>黑白名单设置会立即生效，无需刷新页面</li>
                <li>设置更新后，当前打开的所有网页会自动应用新的设置</li>
              </ul>
            </li>
            <li>
              使用建议:
              <ul>
                <li>对于不希望启用高亮的网站，可将其添加到黑名单</li>
                <li>对于经常需要使用高亮的特定网站，可使用白名单模式</li>
                <li>每个标签页的高亮状态可以单独控制，不受其他标签页影响</li>
                <li>
                  标签页之间的开关状态完全独立，一个标签页的开关不会影响其他标签页
                </li>
              </ul>
            </li>
          </ul>
        </div>
        <div class="help-section">
          <div class="section-header">
            <svg class="section-icon" viewBox="0 0 1024 1024">
              <path
                fill="currentColor"
                d="M218.8 673.2c-15.8 0-28.6-12.8-28.6-28.6V236.8c0-15.8 12.8-28.6 28.6-28.6h586.5c15.8 0 28.6 12.8 28.6 28.6v126.9c0 11 8.9 20 20 20s20-9 20-20V236.8c0-37.8-30.8-68.6-68.6-68.6H218.8c-37.8 0-68.6 30.8-68.6 68.6v407.8c0 37.8 30.8 68.6 68.6 68.6h298.2c11 0 20-8.9 20-20 0-11-8.9-20-20-20H218.8z"
              />
              <path
                fill="currentColor"
                d="M380 485.7h-60.3c-7.7 0-14-6.3-14-14s6.3-14 14-14H380c7.7 0 14 6.3 14 14s-6.2 14-14 14zM502.8 485.7H442.5c-7.7 0-14-6.3-14-14s6.3-14 14-14h60.3c7.7 0 14 6.3 14 14s-6.3 14-14 14zM624.1 485.7h-60.3c-7.7 0-14-6.3-14-14s6.3-14 14-14h60.3c7.7 0 14 6.3 14 14s-6.3 14-14 14z"
              />
              <path
                fill="currentColor"
                d="M590.9 693.4c-30.6-18.7-49.9-41.3-65.3-65.3-8.2-12.9-15.1-26.1-20.6-40.1-9.6 19.7-28.3 43.9-56.2 70.5-4.1 3.9-10.5 3.7-14.4-0.3-3.9-4-3.8-10.5 0.3-14.4 60.6-57.6 69.9-96.7 70.1-97.6 1.2-5.5 6.7-9 12.1-7.7 5.4 1.2 8.9 6.6 7.7 12-0.1 0.5-7.5 36.2 72.5 84.3 4.9 2.9 6.4 9.3 3.4 14.2-3 4.9-9.3 6.4-14.2 3.4-5.6-3.5-10.8-6.6-15.5-9.4-5.2 9.3-13.1 22.1-23.7 34.4 25.9 4.3 51.6 2.9 63.4 2.1 0.6-0.1 1.2-0.1 1.8-0.1 4.5 0 8.6 3.1 9.8 7.5 1.3 5.3-1.9 10.7-7.3 12.1-2.7 0.6-65 14.4-116.8-17.4-5-3-6.5-9.4-3.5-14.3 3-5 9.4-6.5 14.3-3.5 9.4 5.7 19.4 10.3 29.7 13.8 9-10.9 16.1-22.7 20.9-31.3-12.5-8.3-23.3-16.6-32.8-25.2-2.7 7.9-6.6 17.4-12.2 27.4-2.5 4.5-8.2 6.1-12.7 3.5-4.5-2.5-6.1-8.3-3.5-12.7 24.1-43.5 16.6-65.8 16.5-66.1-1.6-5.3 1.4-10.9 6.6-12.5 5.3-1.6 11 1.3 12.6 6.6 0.2 0.7 5.3 18.2-3.9 46.2 5.5 5.2 11.5 10.1 18.3 14.7 16.6 11.3 33.9 20.3 51.8 27 4.8-17.6 21.7-33.7 39.6-36.7 23.8-4 48.2 12.3 54.8 36.2 6.6 23.9-7.1 48-30.9 52-17.9 3-36.8-6.4-47.2-21.7z"
              />
              <path
                fill="currentColor"
                d="M778.7 755.2H521.4c-11 0-20 9-20 20s8.9 20 20 20h257.3c15.8 0 28.6-12.8 28.6-28.6V508.8c0-15.8-12.8-28.6-28.6-28.6H500.5c-15.8 0-28.6 12.8-28.6 28.6v126.9c0 11 8.9 20 20 20s20-9 20-20V508.8c0 0 0-0.1 0.1-0.1l278.1 0.1c0 0 0.1 0 0.1 0.1v257.4c0 0-0.1 0.1-0.1 0.1-3.8-0.1-7.6-0.1-11.4-0.1z"
              />
            </svg>
            <h3>文本解读工具</h3>
          </div>
          <ul>
            <li>
              基本使用:
              <ul>
                <li>选中任意文本后会自动显示翻译工具栏</li>
                <li>点击工具栏上的"翻译文本"按钮将文本翻译成中文</li>
                <li>结果窗口支持拖动，可自由调整位置</li>
              </ul>
            </li>
            <li>
              缩写解释功能:
              <ul>
                <li>优先使用专业数据库查询缩写含义</li>
                <li>支持解释首字母缩写</li>
              </ul>
            </li>
            <li>
              使用限制:
              <ul>
                <li>为保障服务质量，API请求有以下频率限制:</li>
                <li>10秒内最多可发送5次请求</li>
                <li>1分钟内最多可发送20次请求</li>
                <li>1小时内最多可发送200次请求</li>
                <li>超出限制会收到"请稍后再试"的提示</li>
              </ul>
            </li>
            <li>
              翻译功能:
              <ul>
                <li>专注于将各种语言翻译成中文</li>
                <li>自动识别源语言，无需手动选择</li>
                <li>保留原文格式（如Markdown、代码等）</li>
                <li>翻译结果清晰展示，不含警示标记</li>
              </ul>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <script src="js/jquery.js"></script>
  </body>
</html>
