const createHighlighterConfig = () => {
  const config = {
    // 默认的样式类名
    className: "chrome-extension-mutihighlight",
    stylePrefix: "chrome-extension-mutihighlight-style-",

    // 过滤规则
    filterRules: {
      shouldSkipTag(node) {
        return (
          node.tagName &&
          (["SCRIPT", "STYLE", "NOSCRIPT"].includes(node.tagName) ||
            node.isContentEditable ||
            (node.parentNode && node.parentNode.isContentEditable))
        );
      },

      shouldAllowInput(element) {
        return !(
          element &&
          (element.isContentEditable ||
            ["INPUT", "TEXTAREA", "SELECT"].includes(element.tagName) ||
            (element.parentNode &&
              ["SCRIPT", "STYLE", "NOSCRIPT"].includes(
                element.parentNode.tagName
              )))
        );
      },

      isEditable(node, root) {
        while (node && node !== root) {
          if (
            node.tagName &&
            (node.isContentEditable ||
              ["STYLE", "SCRIPT", "NOSCRIPT"].includes(node.tagName))
          )
            return true;
          node = node.parentNode;
        }
        return false;
      },
    },

    // 精简后的性能配置
    performance: {
      // 批处理相关
      batch: {
        size: 30, // 适中的批次大小，平衡效率和响应性
        maxNodes: 1000, // LRU 缓存的最大节点数
        maxTime: 8, // 8ms时间片，平衡处理效率和流畅性
      },

      // 防抖配置
      debounce: {
        input: 500, // 输入防抖
        update: 500, // 更新防抖
      },

      // 节流配置
      throttle: {
        default: 50, // 默认节流时间
      },
    },

    // 由内置的 filterRules 提供了足够的节点过滤功能
    filter: (node, term, totalCounter, counter) => {
      if (term === "the" && counter >= 10) return false;
      return true;
    },
  };

  return config;
};

// 导出配置
(function (global) {
  const config = createHighlighterConfig();

  // 绑定过滤规则中的this
  Object.values(config.filterRules).forEach((fn) => {
    if (typeof fn === "function") {
      config.filterRules[fn.name] = fn.bind(config);
    }
  });

  global.HighlighterConfig = config;
})(typeof window !== "undefined" ? window : global);
