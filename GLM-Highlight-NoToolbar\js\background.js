// 统一的缓存管理器
class CacheManager {
  constructor(options = {}) {
    this.cache = new Map();
    this.ttl = options.ttl || 5 * 60 * 1000; // 5分钟
    this.maxAge = options.maxAge || 30 * 60 * 1000; // 30分钟
    this.maxSize = options.maxSize || 1000;
    this.cleanupInterval = options.cleanupInterval || 2 * 60 * 1000;

    // 启动定时清理
    this.cleanupTimer = setInterval(() => this.cleanup(), this.cleanupInterval);
  }

  set(key, value) {
    // 检查缓存大小
    if (this.cache.size >= this.maxSize) {
      const oldestKey = Array.from(this.cache.keys()).sort(
        (a, b) => this.cache.get(a).timestamp - this.cache.get(b).timestamp
      )[0];
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      lastAccessed: Date.now(),
    });
  }

  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;

    const now = Date.now();
    if (now - item.timestamp > this.maxAge) {
      this.cache.delete(key);
      return null;
    }

    // 更新访问时间
    item.lastAccessed = now;
    return item.value;
  }

  cleanup() {
    const now = Date.now();
    for (const [key, item] of this.cache) {
      if (
        now - item.timestamp > this.maxAge ||
        now - item.lastAccessed > this.ttl
      ) {
        this.cache.delete(key);
      }
    }
  }

  clear() {
    this.cache.clear();
  }

  destroy() {
    clearInterval(this.cleanupTimer);
    this.clear();
  }
}

const background = {
  USER_ID_STORE: "userUUID",
  ACTIVE_STATUS_STORE: "isActive",
  KEYWORDS_STRING_STORE: "fwm_keywordsString",
  KEYWORDS_ARRAY_STORE: "fwm_keywordsArray",
  TAB_ACTIVE_STATUS: "tabActiveStatus",
  DOMAIN_RULES_KEY: "domain_rules",
  TAB_CATEGORY_STATUS: "tabCategoryStatus",
  // 移除了 TRANSLATOR_TOOLBAR_KEY
  URL_ACTIVE_STATUS: "urlActiveStatus",

  // 使用新的缓存管理器
  _cache: new CacheManager({
    ttl: 5 * 60 * 1000,
    maxSize: 1000,
    cleanupInterval: 2 * 60 * 1000,
  }),

  tabActiveStatus: new Map(),
  tabCategoryStatus: new Map(),
  domainRules: null,
  appliedDomainRules: null,

  async setLocalStorage(key, value) {
    try {
      const oldValue = await this.getLocalStorage(key);
      if (oldValue === value) return;

      await chrome.storage.local.set({ [key]: value });
      this._cache.set(key, value);

      await this._broadcastChange(key, value);
    } catch (error) {
      Utils.handleError(error, "setLocalStorage");
    }
  },

  async getLocalStorage(key) {
    try {
      // 先检查缓存
      const cached = this._cache.get(key);
      if (cached !== null) return cached;

      const result = await chrome.storage.local.get(key);
      const value = result[key];

      // 缓存结果
      if (value !== undefined) {
        this._cache.set(key, value);
      }

      return value;
    } catch (error) {
      Utils.handleError(error, "getLocalStorage");
      return null;
    }
  },

  async _broadcastChange(key, value) {
    try {
      const tabs = await chrome.tabs.query({});
      const message = {
        opt: "event",
        event: "storageChanged",
        data: { key, value },
      };

      for (const tab of tabs) {
        if (tab.url && tab.url.match(/^https?:/)) {
          chrome.tabs.sendMessage(tab.id, message).catch(() => {
            // 忽略无法发送消息的标签页
          });
        }
      }
    } catch (error) {
      Utils.handleError(error, "_broadcastChange");
    }
  },

  async setKeywordsString2(keywords, options = {}) {
    try {
      // 处理关键词,确保同一个词只出现一次
      const wordMap = new Map();

      // 提前获取当前存储的关键词列表
      let currentList = null;
      if (options.isTabSpecific) {
        currentList = await this.getLocalStorage(this.KEYWORDS_STRING_STORE);
      }

      const processedKeywords = keywords.reduce((acc, item, idx) => {
        // 获取之前保存的关键词列表，保留原来的status
        let status = item.status;

        // 重要：如果是标签页特定操作，不保存分类开关状态到全局
        if (
          options.isTabSpecific &&
          currentList &&
          Array.isArray(currentList) &&
          currentList[idx]
        ) {
          // 保留原状态，不更新全局分类状态
          status = currentList[idx].status;
        }

        if (item.data) {
          const words = item.data.trim().split(/\s+/).filter(Boolean);
          words.forEach((word) => {
            wordMap.set(word, {
              colour: item.colour,
              words: word,
              categoryIndex: idx,
            });
          });
        }
        return acc;
      }, []);

      // 保存原始分类数据
      await this.setLocalStorage(this.KEYWORDS_STRING_STORE, keywords);

      // 生成扁平化的关键词数组
      const keywordsArray = Array.from(wordMap.values());
      await this.setLocalStorage(this.KEYWORDS_ARRAY_STORE, keywordsArray);

      console.log("GLM-Highlight 关键词已保存:", {
        categories: keywords.length,
        totalWords: keywordsArray.length,
        isTabSpecific: options.isTabSpecific || false,
      });

      return true;
    } catch (error) {
      Utils.handleError(error, "setKeywordsString2");
      return false;
    }
  },

  async getKeywordsString2() {
    try {
      const result = await this.getLocalStorage(this.KEYWORDS_STRING_STORE);
      return result || [];
    } catch (error) {
      Utils.handleError(error, "getKeywordsString2");
      return [];
    }
  },

  async getKeywordsArray() {
    try {
      const result = await this.getLocalStorage(this.KEYWORDS_ARRAY_STORE);
      return result || [];
    } catch (error) {
      Utils.handleError(error, "getKeywordsArray");
      return [];
    }
  },

  async setActiveStatus(status) {
    try {
      await this.setLocalStorage(this.ACTIVE_STATUS_STORE, String(status));
      return true;
    } catch (error) {
      Utils.handleError(error, "setActiveStatus");
      return false;
    }
  },

  async getActiveStatus() {
    try {
      const result = await this.getLocalStorage(this.ACTIVE_STATUS_STORE);
      return result === "true";
    } catch (error) {
      Utils.handleError(error, "getActiveStatus");
      return true; // 默认启用
    }
  },

  async setUrlActiveStatus(status) {
    try {
      const [tab] = await chrome.tabs.query({
        active: true,
        currentWindow: true,
        url: ["http://*/*", "https://*/*"],
      });

      if (!tab || !tab.url) {
        console.warn("无法获取当前标签页URL");
        return false;
      }

      const url = new URL(tab.url);
      const domain = url.hostname;

      // 获取当前URL状态存储
      const urlStatus =
        (await this.getLocalStorage(this.URL_ACTIVE_STATUS)) || {};

      // 更新当前域名的状态
      urlStatus[domain] = status;

      // 保存更新后的状态
      await this.setLocalStorage(this.URL_ACTIVE_STATUS, urlStatus);

      console.log("GLM-Highlight URL状态已更新:", {
        domain: domain,
        status: status,
        tabId: tab.id,
      });

      // 通知当前标签页状态变化
      chrome.tabs
        .sendMessage(tab.id, {
          opt: "event",
          event: "urlActiveStatusChanged",
          data: { domain, status, tabId: tab.id },
        })
        .catch(() => {
          // 忽略发送失败的情况
        });

      return true;
    } catch (error) {
      Utils.handleError(error, "setUrlActiveStatus");
      return false;
    }
  },

  async getUrlActiveStatus(url = null) {
    try {
      let targetUrl = url;

      if (!targetUrl) {
        const [tab] = await chrome.tabs.query({
          active: true,
          currentWindow: true,
          url: ["http://*/*", "https://*/*"],
        });

        if (!tab || !tab.url) {
          return true; // 默认启用
        }
        targetUrl = tab.url;
      }

      const urlObj = new URL(targetUrl);
      const domain = urlObj.hostname;

      // 获取URL状态存储
      const urlStatus =
        (await this.getLocalStorage(this.URL_ACTIVE_STATUS)) || {};

      // 返回该域名的状态，默认为true（启用）
      return urlStatus[domain] !== false;
    } catch (error) {
      Utils.handleError(error, "getUrlActiveStatus");
      return true; // 默认启用
    }
  },

  async getDomainRules() {
    if (this.domainRules) {
      return this.domainRules;
    }

    try {
      const result = await chrome.storage.sync.get(this.DOMAIN_RULES_KEY);
      this.domainRules = result[this.DOMAIN_RULES_KEY] || {
        mode: "blacklist",
        domains: [],
      };
      return this.domainRules;
    } catch (error) {
      Utils.handleError(error, "getDomainRules");
      return { mode: "blacklist", domains: [] };
    }
  },

  async shouldHighlightDomain(url) {
    try {
      const rules = await this.getDomainRules();
      const urlObj = new URL(url);
      const domain = urlObj.hostname;

      const isInList = rules.domains.some((ruleDomain) => {
        if (ruleDomain.startsWith("*.")) {
          const baseDomain = ruleDomain.slice(2);
          return domain === baseDomain || domain.endsWith("." + baseDomain);
        }
        return domain === ruleDomain;
      });

      if (rules.mode === "whitelist") {
        return isInList;
      } else {
        return !isInList;
      }
    } catch (error) {
      Utils.handleError(error, "shouldHighlightDomain");
      return true;
    }
  },

  normalizeUrl(url) {
    try {
      const urlObj = new URL(url);
      const base = `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}`;
      const h = urlObj.hash;
      const normalized = h && h.startsWith("#/") ? `${base}${h}` : base;
      return normalized;
    } catch (error) {
      Utils.handleError(error, "normalizeUrl");
      return url;
    }
  },

  generateUUID() {
    const lut = Array(256)
      .fill()
      .map((_, i) => (i < 16 ? "0" : "") + i.toString(16));
    return () => {
      const d0 = (Math.random() * 0xffffffff) | 0;
      const d1 = (Math.random() * 0xffffffff) | 0;
      const d2 = (Math.random() * 0xffffffff) | 0;
      const d3 = (Math.random() * 0xffffffff) | 0;
      return `${
        lut[d0 & 0xff] +
        lut[(d0 >> 8) & 0xff] +
        lut[(d0 >> 16) & 0xff] +
        lut[(d0 >> 24) & 0xff]
      }-${lut[d1 & 0xff]}${lut[(d1 >> 8) & 0xff]}-${
        lut[((d1 >> 16) & 0x0f) | 0x40]
      }${lut[(d1 >> 24) & 0xff]}-${lut[(d2 & 0x3f) | 0x80]}${
        lut[(d2 >> 8) & 0xff]
      }-${lut[(d2 >> 16) & 0xff]}${lut[(d2 >> 24) & 0xff]}${lut[d3 & 0xff]}${
        lut[(d3 >> 8) & 0xff]
      }${lut[(d3 >> 16) & 0xff]}${lut[(d3 >> 24) & 0xff]}`;
    };
  },

  async getUserId() {
    try {
      let userUUID = await this.getLocalStorage(this.USER_ID_STORE);
      if (!userUUID) {
        userUUID = this.generateUUID();
        await this.setLocalStorage(this.USER_ID_STORE, userUUID);
      }
      return userUUID;
    } catch (error) {
      Utils.handleError(error, "getUserId");
      return null;
    }
  },

  async getActiveStatus() {
    try {
      const globalStatus = await this.getLocalStorage(this.ACTIVE_STATUS_STORE);
      if (globalStatus === null || globalStatus === undefined) {
        return true;
      }
      return globalStatus !== false;
    } catch (error) {
      Utils.handleError(error, "getActiveStatus");
      return true;
    }
  },

  async setActiveStatus(status) {
    try {
      const newStatus = Boolean(status);
      await this.setLocalStorage(this.ACTIVE_STATUS_STORE, newStatus);

      const [activeTab] = await chrome.tabs.query({
        active: true,
        currentWindow: true,
        url: ["http://*/*", "https://*/*"],
      });

      if (activeTab) {
        const shouldHighlight = await this.shouldHighlightDomain(activeTab.url);

        let tabStatus;
        try {
          const normalizedUrl = this.normalizeUrl(activeTab.url);
          if (this.tabActiveStatus.has(normalizedUrl)) {
            tabStatus = this.tabActiveStatus.get(normalizedUrl);
          } else {
            const key = `${this.URL_ACTIVE_STATUS}_${normalizedUrl}`;
            const result = await chrome.storage.local.get([key]);
            tabStatus = result[key];
          }
        } catch (e) {}

        try {
          const payload = {
            opt: "event",
            event: "updateHighlightStatus",
            shouldHighlight,
          };
          if (tabStatus !== undefined) {
            payload.tabStatus = tabStatus;
          } else {
            payload.globalActive = newStatus;
          }

          const alive = await chrome.tabs.get(activeTab.id).catch(() => null);
          if (alive) {
            await chrome.tabs
              .sendMessage(activeTab.id, payload)
              .catch(() => {});
          }
        } catch (err) {
          // 忽略发送失败
        }
      }
    } catch (error) {
      Utils.handleError(error, "setActiveStatus");
    }
  },

  async updateTabHighlightStatus(tabId, url) {
    try {
      const shouldHighlight = await this.shouldHighlightDomain(url);
      const normalizedUrl = this.normalizeUrl(url);

      let tabStatus;
      if (this.tabActiveStatus.has(normalizedUrl)) {
        tabStatus = this.tabActiveStatus.get(normalizedUrl);
      } else {
        const key = `${this.URL_ACTIVE_STATUS}_${normalizedUrl}`;
        const result = await chrome.storage.local.get([key]);
        tabStatus = result[key];
        if (tabStatus !== undefined) {
          this.tabActiveStatus.set(normalizedUrl, tabStatus);
        }
      }

      const globalStatus = await this.getActiveStatus();
      const finalStatus = tabStatus !== undefined ? tabStatus : globalStatus;

      const payload = {
        opt: "event",
        event: "updateHighlightStatus",
        shouldHighlight,
        tabStatus: finalStatus,
      };

      const alive = await chrome.tabs.get(tabId).catch(() => null);
      if (alive) {
        await chrome.tabs.sendMessage(tabId, payload).catch(() => {});
      }
    } catch (error) {
      Utils.handleError(error, "updateTabHighlightStatus");
    }
  },

  async updateAppliedRules() {
    try {
      this.appliedDomainRules = await this.getDomainRules();
    } catch (error) {
      Utils.handleError(error, "updateAppliedRules");
    }
  },

  _verifyMessage(message) {
    return (
      message && typeof message === "object" && message.opt && message.func
    );
  },

  async processBatchMessages(messages) {
    const results = [];
    for (const msg of messages) {
      try {
        const func = this[msg.func];
        if (typeof func === "function") {
          const result = await func.apply(this, msg.args || []);
          results.push({ success: true, result });
        } else {
          results.push({ success: false, error: "函数不存在" });
        }
      } catch (error) {
        results.push({ success: false, error: error.message });
      }
    }
    return results;
  },
};

// 消息处理
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  try {
    if (message.opt === "event") {
      switch (message.event) {
        case "reapplyHighlights":
          background.reapplyHighlights().then(() => {
            sendResponse({ success: true });
          });
          break;
        case "domainRulesUpdated":
          background.domainRules = message.data;
          background.updateAppliedRules().then(() => {
            sendResponse({ success: true });
          });
          break;
        // 移除了 translatorToolbarUpdated 事件处理
        default:
          console.warn("未处理的事件类型:", message.event);
          sendResponse({ success: false, error: "未处理的事件类型" });
      }
      return true;
    }

    if (message.opt === "rpc") {
      if (!background._verifyMessage(message)) {
        sendResponse({ success: false, error: "无效的RPC消息格式" });
        return true;
      }

      if (message.func === "batch") {
        background.processBatchMessages(message.args).then(sendResponse);
        return true;
      }

      const func = background[message.func];
      if (typeof func === "function") {
        func.apply(background, message.args || []).then(sendResponse);
        return true;
      } else {
        sendResponse({ success: false, error: "函数不存在" });
      }
      return true;
    }
  } catch (error) {
    console.error("处理消息时出错:", error);
    sendResponse({ success: false, error: error.message });
  }
  return true;
});

// 标签页事件处理
chrome.tabs.onRemoved.addListener((tabId) => {
  background.tabActiveStatus.delete(tabId);
  chrome.storage.local.remove(`${background.TAB_ACTIVE_STATUS}_${tabId}`);

  const keysToRemove = [];
  background.tabCategoryStatus.forEach((value, key) => {
    if (key.startsWith(`${tabId}_`)) {
      background.tabCategoryStatus.delete(key);
    }
  });

  const categoryKeyPrefix = `${background.TAB_CATEGORY_STATUS}_${tabId}_`;
  chrome.storage.local.get(null, (items) => {
    Object.keys(items).forEach((key) => {
      if (key.startsWith(categoryKeyPrefix)) {
        keysToRemove.push(key);
      }
    });

    if (keysToRemove.length > 0) {
      chrome.storage.local.remove(keysToRemove);
    }
  });
});

// 注册右键菜单和初始化插件
chrome.runtime.onInstalled.addListener(async () => {
  try {
    const currentStatus = await chrome.storage.local.get([
      background.ACTIVE_STATUS_STORE,
    ]);

    if (!currentStatus[background.ACTIVE_STATUS_STORE]) {
      await chrome.storage.local.set({
        [background.ACTIVE_STATUS_STORE]: "true",
      });
    }

    chrome.contextMenus.create({
      id: "add-to-category",
      title: "添加到高亮分类",
      contexts: ["selection"],
    });

    chrome.contextMenus.create({
      id: "remove-highlight",
      title: "删除高亮",
      contexts: ["selection"],
    });
  } catch (error) {
    console.error("初始化插件失败:", error);
  }
});

// 处理右键菜单点击
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === "add-to-category") {
    chrome.tabs
      .sendMessage(tab.id, {
        type: "add-to-category",
      })
      .catch(() => {});
  } else if (info.menuItemId === "remove-highlight") {
    chrome.tabs
      .sendMessage(tab.id, {
        type: "remove-highlight",
      })
      .catch(() => {});
  } else if (info.menuItemId === "options") {
    chrome.runtime.openOptionsPage();
  }
});

// 处理快捷键
chrome.commands.onCommand.addListener((command, tab) => {
  if (command === "add-to-category") {
    chrome.tabs
      .sendMessage(tab.id, {
        type: "add-to-category",
      })
      .catch(() => {});
  } else if (command === "remove-highlight") {
    chrome.tabs
      .sendMessage(tab.id, {
        type: "remove-highlight",
      })
      .catch(() => {});
  }
});

// 移除了划词工具栏相关的脚本注入逻辑

async function setupContextMenu() {
  try {
    await chrome.contextMenus.removeAll();

    chrome.contextMenus.create({
      id: "options",
      title: "网站高亮设置",
      contexts: ["action"],
    });
  } catch (error) {
    console.error("设置上下文菜单失败:", error);
  }
}

// 监听tab更新事件
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (changeInfo.status === "complete" && tab.url) {
    await background.updateAppliedRules();
    await background.updateTabHighlightStatus(tabId, tab.url);
  }
});

// 监听tab激活事件
chrome.tabs.onActivated.addListener(async (activeInfo) => {
  try {
    const tab = await chrome.tabs.get(activeInfo.tabId);
    if (tab.url) {
      await background.updateTabHighlightStatus(tab.id, tab.url);
    }
  } catch (error) {
    console.error("标签激活处理失败:", error);
  }
});

// 初始化扩展
async function initializeExtension() {
  await setupContextMenu();
  await background.getDomainRules();
}

chrome.runtime.onInstalled.addListener(initializeExtension);
